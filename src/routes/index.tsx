import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { ArrowRight, Building, FileText, Upload } from 'lucide-react'
import { useState } from 'react'

export const Route = createFileRoute('/')({
  component: LandingPage,
})

function LandingPage() {
  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const navigate = useNavigate()

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileUpload()
  }

  const handleFileUpload = () => {
    setIsUploading(true)
    setTimeout(() => {
      navigate({ to: '/dashboard' })
    }, 2000)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleFileUpload()
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50 flex flex-col items-center justify-center p-6">
      <div className="max-w-4xl w-full text-center space-y-8">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <Building className="h-10 w-10 text-blue-600" />
            <h1 className="text-4xl font-bold text-gray-900">
              澳門商業登記報告
            </h1>
          </div>
          <h2 className="text-2xl font-semibold text-gray-700">
            數位化儀表板 Demo
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            將傳統商業登記PDF報告轉化為直觀、互動的數位化儀表板，
            快速擷取公司概況、股東變動及歷史資訊
          </p>
        </div>

        {/* Upload Area */}
        <div className="max-w-md mx-auto">
          <div
            className={`relative border-2 border-dashed rounded-2xl p-12 transition-all duration-300 cursor-pointer group ${
              isDragOver
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-300 bg-white hover:border-blue-400 hover:bg-blue-50/50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleFileUpload}
            onKeyDown={handleKeyDown}
            tabIndex={0}
            role="button"
            aria-label="Upload business registration report"
          >
            {isUploading ? (
              <div className="space-y-4">
                <div className="w-12 h-12 mx-auto border-4 border-blue-600 border-t-transparent rounded-full animate-spin" />
                <p className="text-blue-600 font-medium">解析報告中...</p>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="h-12 w-12 text-gray-400 mx-auto group-hover:text-blue-500 transition-colors" />
                <div className="space-y-2">
                  <p className="text-lg font-medium text-gray-700">
                    上傳商業登記報告
                  </p>
                  <p className="text-sm text-gray-500">
                    拖曳PDF檔案至此處或點擊選擇檔案
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Demo Button */}
        <div className="pt-8">
          <button
            type="button"
            onClick={() => navigate({ to: '/dashboard' })}
            className="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <span>查看範例儀表板</span>
            <ArrowRight className="h-5 w-5" />
          </button>
        </div>

        {/* Demo Preview */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-12">
          <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">
            從紙本報告到數位儀表板
          </h3>
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div className="text-center">
              <div className="bg-gray-100 rounded-lg p-6 mb-4">
                <FileText className="h-16 w-16 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 font-medium">傳統PDF報告</p>
                <p className="text-sm text-gray-500 mt-2">
                  • 15頁密集文字<br/>
                  • 15項歷史事件<br/>
                  • 5位股東變動<br/>
                  • 6次資本變更
                </p>
              </div>
            </div>
            <div className="text-center">
              <div className="bg-gradient-to-br from-blue-50 to-emerald-50 rounded-lg p-6 mb-4">
                <Building className="h-16 w-16 text-blue-600 mx-auto mb-2" />
                <p className="text-gray-900 font-medium">智能儀表板</p>
                <p className="text-sm text-emerald-600 mt-2">
                  • 8個關鍵指標卡片<br/>
                  • 股權結構圓餅圖<br/>
                  • 互動式歷史時間軸<br/>
                  • 一鍵查看詳細資料
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-4 gap-6 pt-8">
          <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow group">
            <FileText className="h-8 w-8 text-blue-600 mb-4 group-hover:scale-110 transition-transform" />
            <h3 className="font-semibold text-gray-900 mb-2">AI智能解析</h3>
            <p className="text-gray-600 text-sm">
              自動識別並提取PDF中的公司資訊、股東結構、歷史變更記錄
            </p>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow group">
            <Building className="h-8 w-8 text-emerald-600 mb-4 group-hover:scale-110 transition-transform" />
            <h3 className="font-semibold text-gray-900 mb-2">視覺化呈現</h3>
            <p className="text-gray-600 text-sm">
              複雜數據轉化為直觀圖表，股權分布一目了然
            </p>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow group">
            <ArrowRight className="h-8 w-8 text-orange-600 mb-4 group-hover:scale-110 transition-transform" />
            <h3 className="font-semibold text-gray-900 mb-2">互動式探索</h3>
            <p className="text-gray-600 text-sm">
              點擊展開歷史記錄，追蹤公司發展軌跡和股權變化
            </p>
          </div>
          <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow group">
            <Upload className="h-8 w-8 text-purple-600 mb-4 group-hover:scale-110 transition-transform" />
            <h3 className="font-semibold text-gray-900 mb-2">秒級處理</h3>
            <p className="text-gray-600 text-sm">
              上傳PDF後即時生成完整分析報告，大幅提升工作效率
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
