import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { 
  AlertTriangle,
  ArrowLeft, 
  Building, 
  FileText,
  TrendingUp, 
  Users 
} from 'lucide-react'
import { useState } from 'react'
import { CompanyInfoSection } from '../components/CompanyInfoSection'
import { DocumentsSection } from '../components/DocumentsSection'
import { ShareholdingSection } from '../components/ShareholdingSection'
import { SummaryCards } from '../components/SummaryCards'
import { companyData } from '../data/business-registration-data'

export const Route = createFileRoute('/dashboard')({
  component: DashboardPage,
})

function DashboardPage() {
  const navigate = useNavigate()
  const [activeSection, setActiveSection] = useState<string>('overview')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={() => navigate({ to: '/' })}
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <span>返回</span>
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-3">
                <Building className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {companyData.company_info.current_name.zh}
                  </h1>
                  <p className="text-sm text-gray-500">
                    登記編號: {companyData.company_info.registration_number}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                報告日期: {companyData.company_info.report_as_of_date}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: '總覽', icon: Building },
              { id: 'shareholders', label: '股權結構', icon: Users },
              { id: 'changes', label: '變動記錄', icon: TrendingUp },
              { id: 'documents', label: '文件', icon: FileText }
            ].map((item) => {
              const Icon = item.icon
              return (
                <button
                  type="button"
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors ${
                    activeSection === item.id
                      ? 'border-blue-600 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span className="font-medium">{item.label}</span>
                </button>
              )
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        <div className="space-y-8">
          {/* Summary Cards - Always visible */}
          <SummaryCards data={companyData} />

          {/* Content Sections */}
          {activeSection === 'overview' && (
            <div className="space-y-8">
              {/* Quick Overview Grid */}
              <div className="grid lg:grid-cols-3 gap-6">
                {/* Current Status Card */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200">
                  <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                    <Building className="h-5 w-5 mr-2" />
                    公司現況
                  </h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-blue-700">當前地址:</span>
                      <span className="text-blue-900 font-medium text-right max-w-[200px]">
                        {companyData.company_info.current_address}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-700">營業範圍:</span>
                      <span className="text-blue-900 font-medium">6大領域</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-700">經營狀態:</span>
                      <span className="text-green-600 font-semibold">正常營運</span>
                    </div>
                  </div>
                </div>

                {/* Shareholding Overview */}
                <div className="bg-gradient-to-br from-emerald-50 to-green-100 rounded-xl p-6 border border-emerald-200">
                  <h3 className="text-lg font-semibold text-emerald-900 mb-4 flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    股權概況
                  </h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-emerald-700">現任股東:</span>
                      <span className="text-emerald-900 font-medium">4位</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-emerald-700">最大股東:</span>
                      <span className="text-emerald-900 font-medium">陳錦輝 (36%)</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-emerald-700">國際投資:</span>
                      <span className="text-emerald-900 font-medium">30%</span>
                    </div>
                  </div>
                </div>

                {/* Recent Activities */}
                <div className="bg-gradient-to-br from-orange-50 to-yellow-100 rounded-xl p-6 border border-orange-200">
                  <h3 className="text-lg font-semibold text-orange-900 mb-4 flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    最近活動
                  </h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-orange-700">最近變更:</span>
                      <span className="text-orange-900 font-medium">2024-12-20</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-orange-700">變更類型:</span>
                      <span className="text-orange-900 font-medium">董事變更</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-orange-700">歷史事件:</span>
                      <span className="text-orange-900 font-medium">15項</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Detailed Company Info */}
              <CompanyInfoSection data={companyData} />
            </div>
          )}

          {activeSection === 'shareholders' && (
            <ShareholdingSection data={companyData} expanded />
          )}

          {activeSection === 'changes' && (
            <CompanyInfoSection data={companyData} showTimeline />
          )}

          {activeSection === 'documents' && (
            <DocumentsSection data={companyData} />
          )}

          {/* Important Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-yellow-800">重要聲明</h3>
                <p className="text-sm text-yellow-700 mt-1">
                  本報告僅作資訊用途，不具備證明的效力。如需正式證明文件，請向澳門特別行政區商業及動產登記局申請。
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}