import { useState } from 'react'
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip, Legend, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts'
import { Users, Eye, EyeOff, TrendingUp, BarChart3 } from 'lucide-react'

interface ShareholdingSectionProps {
  data: any
  expanded?: boolean
}

export function ShareholdingSection({ data, expanded = false }: ShareholdingSectionProps) {
  const [showHistoricalShareholders, setShowHistoricalShareholders] = useState(false)

  const activeShareholders = data.shareholders.filter((s: any) => s.current_stake > 0)
  const historicalShareholders = data.shareholders.filter((s: any) => s.current_stake === 0)

  const pieData = activeShareholders.map((shareholder: any) => ({
    name: shareholder.name,
    value: shareholder.current_stake,
    percentage: ((shareholder.current_stake / data.company_info.current_capital) * 100).toFixed(1)
  }))

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']

  // Process historical shareholding data for stacked bar chart
  const processHistoricalData = () => {
    const yearlyData: any = {}
    
    // Get all events with capital changes to track total capital over time
    const capitalEvents = data.history.filter((event: any) => 
      event.details.capital || event.details.new_capital || event.details.old_capital
    )
    
    // Build yearly snapshots
    data.shareholders.forEach((shareholder: any) => {
      shareholder.stake_history.forEach((entry: any) => {
        const year = new Date(entry.date).getFullYear()
        
        if (!yearlyData[year]) {
          yearlyData[year] = {
            year: year.toString(),
            totalCapital: 0
          }
        }
        
        // Find the capital at this time
        const relevantCapitalEvent = capitalEvents.find((event: any) => 
          new Date(event.date).getTime() <= new Date(entry.date).getTime()
        ) || capitalEvents[capitalEvents.length - 1]
        
        const totalCapital = relevantCapitalEvent?.details?.new_capital || 
                           relevantCapitalEvent?.details?.capital || 
                           data.company_info.current_capital
        
        yearlyData[year].totalCapital = totalCapital
        yearlyData[year][shareholder.name] = ((entry.stake / totalCapital) * 100).toFixed(1)
      })
    })
    
    return Object.values(yearlyData).sort((a: any, b: any) => parseInt(a.year) - parseInt(b.year))
  }

  const historicalChartData = processHistoricalData()
  const uniqueShareholders = [...new Set(data.shareholders.map((s: any) => s.name))]

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium">{data.name}</p>
          <p className="text-sm text-gray-600">
            持股: {data.value ? data.value.toLocaleString() : '0'} ({data.percentage || '0'}%)
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
          <Users className="h-6 w-6 text-blue-600" />
          <span>股權結構與變動</span>
        </h2>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Pie Chart */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">股權分布</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ percentage }) => `${percentage}%`}
                >
                  {pieData.map((_: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Current Shareholders */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">現任股東</h3>
          <div className="space-y-4">
            {activeShareholders.map((shareholder: any, index: number) => (
              <div key={shareholder.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{shareholder.name}</h4>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <span className="text-sm font-medium text-gray-700">
                      {((shareholder.current_stake / data.company_info.current_capital) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>股額: {shareholder.current_stake.toLocaleString()} {data.company_info.currency}</p>
                  <p>性別: {shareholder.gender} | 婚姻狀況: {shareholder.marital_status}</p>
                  {shareholder.regime && <p>財產制度: {shareholder.regime}</p>}
                  {shareholder.address && (
                    <p className="text-xs text-gray-500">
                      地址: {shareholder.address.length > 30 ? 
                        `${shareholder.address.substring(0, 30)}...` : 
                        shareholder.address}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Historical Shareholding Chart */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            <span>歷史股權變化</span>
          </h3>
          <div className="text-sm text-gray-500">
            2018-2024 年度股權分布
          </div>
        </div>
        
        <div className="h-96 mb-4">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={historicalChartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="year" 
                tick={{ fontSize: 12 }}
                axisLine={{ stroke: '#E5E7EB' }}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                axisLine={{ stroke: '#E5E7EB' }}
                label={{ value: '持股比例 (%)', angle: -90, position: 'insideLeft' }}
              />
              <Tooltip 
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length) {
                    return (
                      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                        <p className="font-medium mb-2">{label} 年</p>
                        {payload.map((entry: any, index: number) => (
                          <p key={index} className="text-sm" style={{ color: entry.color }}>
                            {entry.dataKey}: {entry.value}%
                          </p>
                        ))}
                      </div>
                    )
                  }
                  return null
                }}
              />
              <Legend />
              {(uniqueShareholders as string[]).map((shareholder: string, index: number) => (
                <Bar
                  key={shareholder}
                  dataKey={shareholder}
                  stackId="shareholding"
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 text-sm">
          {(uniqueShareholders as string[]).map((shareholder: string, index: number) => {
            const shareholderData = data.shareholders.find((s: any) => s.name === shareholder)
            const isActive = shareholderData?.current_stake > 0
            return (
              <div key={shareholder} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                />
                <span className={`${isActive ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
                  {shareholder}
                </span>
                {!isActive && (
                  <span className="text-xs text-red-500 bg-red-50 px-1 rounded">已退出</span>
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* Historical Shareholders Details */}
      {historicalShareholders.length > 0 && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">歷史股東詳情</h3>
            <button
              onClick={() => setShowHistoricalShareholders(!showHistoricalShareholders)}
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
            >
              {showHistoricalShareholders ? (
                <>
                  <EyeOff className="h-4 w-4" />
                  <span>隱藏</span>
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4" />
                  <span>查看詳情</span>
                </>
              )}
            </button>
          </div>
          
          {showHistoricalShareholders && (
            <div className="space-y-4">
              {historicalShareholders.map((shareholder: any) => (
                <div key={shareholder.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-700">{shareholder.name}</h4>
                    <span className="text-sm text-red-600 font-medium">已退出</span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>性別: {shareholder.gender} | 婚姻狀況: {shareholder.marital_status}</p>
                    <p>最後持股: {shareholder.stake_history && shareholder.stake_history.length > 0 
                      ? shareholder.stake_history[shareholder.stake_history.length - 1].stake.toLocaleString() 
                      : (shareholder.initial_stake || 0).toLocaleString()} {data.company_info.currency}</p>
                    {shareholder.address && (
                      <p className="text-xs text-gray-500">
                        地址: {shareholder.address}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Shareholding Timeline */}
      {expanded && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            <span>股權變動時間軸</span>
          </h3>
          <div className="space-y-4">
            {data.history
              .filter((event: any) => 
                event.type.includes('股份') || 
                event.type.includes('股東') || 
                event.type.includes('增資') || 
                event.type.includes('減資')
              )
              .map((event: any) => (
                <div key={event.event_id} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2" />
                  <div className="flex-grow">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900">{event.type}</h4>
                      <span className="text-sm text-gray-500">{event.date}</span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {event.type.includes('增資') && event.details.capital_increase_amount && (
                        <p>增資金額: {event.details.capital_increase_amount.toLocaleString()} {data.company_info.currency}</p>
                      )}
                      {event.type.includes('減資') && event.details.capital_reduction_amount && (
                        <p>減資金額: {event.details.capital_reduction_amount.toLocaleString()} {data.company_info.currency}</p>
                      )}
                      {event.details.share_transfer && (
                        <p>股份轉讓: {event.details.share_transfer.amount.toLocaleString()} {data.company_info.currency}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  )
}