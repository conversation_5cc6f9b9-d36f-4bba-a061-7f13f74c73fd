import { Briefcase, Building, Clock, DollarSign, Eye, EyeOff, MapPin, Users } from 'lucide-react'
import { useState } from 'react'
import type { AddressChangeDetails, BusinessScopeChangeDetails, EstablishmentDetails, HistoryEvent } from '../data/business-registration-data'
import type { CompanyInfoSectionProps, HistoryToggleState, LatestCompanyInfo } from '../types/component-types'

export function CompanyInfoSection({ data, showTimeline = false }: CompanyInfoSectionProps) {
  const [showHistory, setShowHistory] = useState<HistoryToggleState>({})

  const toggleHistory = (section: string) => {
    setShowHistory(prev => ({ ...prev, [section]: !prev[section] }))
  }

  const getLatestInfo = (): LatestCompanyInfo => {
    const addressEvent = data.history.find((h: HistoryEvent) =>
      'new_address' in h.details
    )?.details as AddressChangeDetails | undefined

    const businessScopeEvent = data.history.find((h: HistoryEvent) =>
      'new_business_scope' in h.details
    )?.details as BusinessScopeChangeDetails | undefined

    const establishmentEvent = data.history[0]?.details as EstablishmentDetails

    return {
      address: addressEvent?.new_address || establishmentEvent?.address || '',
      businessScope: businessScopeEvent?.new_business_scope || establishmentEvent?.business_scope || '',
      capital: data.company_info.current_capital
    }
  }

  const info = getLatestInfo()

  if (showTimeline) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
            <Clock className="h-6 w-6 text-blue-600" />
            <span>完整變動記錄</span>
          </h2>
        </div>

        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <div className="space-y-6">
            {data.history.map((event: HistoryEvent, index: number) => (
              <div key={event.event_id} className="relative">
                {index !== data.history.length - 1 && (
                  <div className="absolute left-4 top-8 w-0.5 h-full bg-gray-200" />
                )}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-grow">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-gray-900">{event.type}</h3>
                        <span className="text-sm text-gray-500">{event.date}</span>
                      </div>
                      <div className="text-sm text-gray-600 space-y-1">
                        {'name' in event.details && (
                          <p><strong>公司名稱:</strong> {event.details.name.zh}</p>
                        )}
                        {'address' in event.details && (
                          <p><strong>法人住所:</strong> {event.details.address}</p>
                        )}
                        {'new_address' in event.details && (
                          <p><strong>新址:</strong> {event.details.new_address}</p>
                        )}
                        {'business_scope' in event.details && (
                          <p><strong>營業範圍:</strong> {event.details.business_scope}</p>
                        )}
                        {'capital' in event.details && (
                          <p><strong>資本額:</strong> {event.details.capital.toLocaleString()} {data.company_info.currency}</p>
                        )}
                        {'new_capital' in event.details && (
                          <p><strong>新資本額:</strong> {event.details.new_capital.toLocaleString()} {data.company_info.currency}</p>
                        )}
                        {'documents' in event.details && (
                          <p><strong>相關文件:</strong> {event.details.documents.join(', ')}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
          <Building className="h-6 w-6 text-blue-600" />
          <span>公司資訊</span>
        </h2>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">基本資料</h3>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
              <div className="flex-grow">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-gray-900">法人住所</p>
                  <button
                    type="button"
                    onClick={() => toggleHistory('address')}
                    className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
                  >
                    {showHistory.address ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    <span>歷史</span>
                  </button>
                </div>
                <p className="text-gray-600 text-sm mt-1">{info.address}</p>
                
                {showHistory.address && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-700 mb-2">住所變更歷史</h4>
                    {data.history
                      .filter((h: HistoryEvent) => h.type.includes('住所'))
                      .map((h: HistoryEvent) => (
                        <div key={h.event_id} className="text-sm text-gray-600 mb-2">
                          <p className="font-medium">{h.date}</p>
                          {'old_address' in h.details && <p>從: {h.details.old_address}</p>}
                          {'new_address' in h.details && <p>到: {h.details.new_address}</p>}
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Briefcase className="h-5 w-5 text-gray-400 mt-0.5" />
              <div className="flex-grow">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-gray-900">所營事業</p>
                  <button
                    type="button"
                    onClick={() => toggleHistory('business')}
                    className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
                  >
                    {showHistory.business ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    <span>歷史</span>
                  </button>
                </div>
                <p className="text-gray-600 text-sm mt-1">{info.businessScope}</p>
                
                {showHistory.business && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-700 mb-2">營業範圍變更歷史</h4>
                    {data.history
                      .filter((h: HistoryEvent) => h.type.includes('事業'))
                      .map((h: HistoryEvent) => (
                        <div key={h.event_id} className="text-sm text-gray-600 mb-2">
                          <p className="font-medium">{h.date}</p>
                          {'old_business_scope' in h.details && <p>從: {h.details.old_business_scope}</p>}
                          {'new_business_scope' in h.details && <p>到: {h.details.new_business_scope}</p>}
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <DollarSign className="h-5 w-5 text-gray-400 mt-0.5" />
              <div className="flex-grow">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-gray-900">資本額</p>
                  <button
                    type="button"
                    onClick={() => toggleHistory('capital')}
                    className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
                  >
                    {showHistory.capital ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    <span>歷史</span>
                  </button>
                </div>
                <p className="text-gray-600 text-sm mt-1">
                  {info.capital.toLocaleString()} {data.company_info.currency}
                </p>
                
                {showHistory.capital && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-700 mb-2">資本額變更歷史</h4>
                    {data.history
                      .filter((h: HistoryEvent) => h.type.includes('資') && ('old_capital' in h.details || 'capital' in h.details))
                      .map((h: HistoryEvent) => (
                        <div key={h.event_id} className="text-sm text-gray-600 mb-2">
                          <p className="font-medium">{h.date} - {h.type}</p>
                          {'old_capital' in h.details && (
                            <p>從: {h.details.old_capital.toLocaleString()} 到: {h.details.new_capital.toLocaleString()}</p>
                          )}
                          {'capital' in h.details && !('old_capital' in h.details) && (
                            <p>設立資本: {h.details.capital.toLocaleString()}</p>
                          )}
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Administrative Management */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">行政管理成員</h3>
          <div className="space-y-4">
            {data.current_administrators.map((admin) => (
              <div key={`${admin.name}-${admin.appointment_date}`} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <Users className="h-5 w-5 text-gray-400" />
                  <div>
                    <h4 className="font-medium text-gray-900">{admin.name}</h4>
                    <p className="text-sm text-gray-600">{admin.title}</p>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  <p>簽名方式: {admin.signature_method}</p>
                  <p>任職日期: {admin.appointment_date}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4">
            <button
              type="button"
              onClick={() => toggleHistory('admin')}
              className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
            >
              {showHistory.admin ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              <span>查看歷史管理成員</span>
            </button>
            
            {showHistory.admin && (
              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-700 mb-2">歷史管理成員</h4>
                {data.history
                  .filter((h: HistoryEvent) => 'administrators' in h.details || 'administrator_change' in h.details)
                  .map((h: HistoryEvent) => (
                    <div key={h.event_id} className="text-sm text-gray-600 mb-2">
                      <p className="font-medium">{h.date}</p>
                      {'administrator_change' in h.details && (
                        <>
                          <p>離任: {h.details.administrator_change.old_manager}</p>
                          <p>新任: {h.details.administrator_change.new_manager}</p>
                        </>
                      )}
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}