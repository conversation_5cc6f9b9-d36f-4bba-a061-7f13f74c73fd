import { Building, Calendar, Clock, DollarSign, FileText, Globe, TrendingUp, Users } from 'lucide-react'
import type { ShareholderWithHistory } from '../data/business-registration-data'
import type { SummaryCardsSectionProps } from '../types/component-types'

export function SummaryCards({ data }: SummaryCardsSectionProps) {
  const activeShareholdersCount = data.shareholders.filter((s: ShareholderWithHistory) => s.current_stake > 0).length
  const totalHistoricalShareholders = data.shareholders.length
  const businessAge = Math.floor((new Date(data.company_info.report_as_of_date).getTime() - new Date(data.company_info.establishment_date).getTime()) / (1000 * 60 * 60 * 24 * 365))
  const hasInternationalShareholder = data.shareholders.some((s: ShareholderWithHistory) => s.address?.includes('新加坡'))

  const cards = [
    {
      title: '公司名稱',
      value: data.company_info.current_name.zh,
      subtitle: `成立 ${businessAge} 年`,
      icon: Building,
      color: 'blue'
    },
    {
      title: '登記編號',
      value: data.company_info.registration_number,
      subtitle: `${data.company_info.total_historical_events} 項歷史事件`,
      icon: FileText,
      color: 'emerald'
    },
    {
      title: '現行資本',
      value: `${data.company_info.current_capital.toLocaleString()} ${data.company_info.currency}`,
      subtitle: `${data.company_info.total_capital_changes} 次資本變動`,
      icon: DollarSign,
      color: 'orange'
    },
    {
      title: '現任股東',
      value: `${activeShareholdersCount} / ${totalHistoricalShareholders}`,
      subtitle: hasInternationalShareholder ? '含國際股東' : '本地股東',
      icon: Users,
      color: 'purple'
    },
    {
      title: '最後更新',
      value: data.company_info.report_as_of_date,
      subtitle: '官方登記資料',
      icon: Calendar,
      color: 'pink'
    },
    {
      title: '營業範圍',
      value: '6 大業務領域',
      subtitle: '含AI及金融科技',
      icon: TrendingUp,
      color: 'indigo'
    },
    {
      title: '公司狀況',
      value: '正常營運',
      subtitle: '2位行政管理成員',
      icon: Clock,
      color: 'green'
    },
    {
      title: '業務類型',
      value: hasInternationalShareholder ? '國際合資' : '本地企業',
      subtitle: '科技創新公司',
      icon: Globe,
      color: 'cyan'
    }
  ]

  const colorClasses = {
    blue: 'text-blue-600 bg-blue-100',
    emerald: 'text-emerald-600 bg-emerald-100',
    orange: 'text-orange-600 bg-orange-100',
    purple: 'text-purple-600 bg-purple-100',
    pink: 'text-pink-600 bg-pink-100',
    indigo: 'text-indigo-600 bg-indigo-100',
    green: 'text-green-600 bg-green-100',
    cyan: 'text-cyan-600 bg-cyan-100'
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {cards.map((card) => {
        const Icon = card.icon
        return (
          <div
            key={card.title}
            className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200 border border-gray-100 hover:border-gray-200 group"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-2 rounded-lg ${colorClasses[card.color as keyof typeof colorClasses]} group-hover:scale-110 transition-transform duration-200`}>
                <Icon className="h-5 w-5" />
              </div>
            </div>
            <h3 className="text-sm font-medium text-gray-500 mb-1">{card.title}</h3>
            <p className="text-lg font-bold text-gray-900 leading-tight mb-1">{card.value}</p>
            {card.subtitle && (
              <p className="text-xs text-gray-400">{card.subtitle}</p>
            )}
          </div>
        )
      })}
    </div>
  )
}