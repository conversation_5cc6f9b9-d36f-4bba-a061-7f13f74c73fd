import { FileText, Calendar, ExternalLink } from 'lucide-react'

interface DocumentsSectionProps {
  data: any
}

export function DocumentsSection({ data }: DocumentsSectionProps) {
  // Collect all documents from history
  const allDocuments = data.history.reduce((docs: any[], event: any) => {
    if (event.details.documents) {
      event.details.documents.forEach((doc: string) => {
        docs.push({
          name: doc,
          date: event.date,
          event_type: event.type,
          event_id: event.event_id
        })
      })
    }
    return docs
  }, [])

  // Group documents by type
  const documentGroups = allDocuments.reduce((groups: any, doc: any) => {
    const key = doc.name
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(doc)
    return groups
  }, {})

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
          <FileText className="h-6 w-6 text-blue-600" />
          <span>文件與備註</span>
        </h2>
      </div>

      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">相關文件清單</h3>
        
        <div className="space-y-4">
          {Object.entries(documentGroups).map(([docType, documents]) => (
            <div key={docType} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                  <FileText className="h-4 w-4 text-blue-600" />
                  <span>{docType}</span>
                </h4>
                <span className="text-sm text-gray-500">
                  {(documents as any[]).length} 次提及
                </span>
              </div>
              
              <div className="space-y-2">
                {(documents as any[]).map((doc, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-gray-600">{doc.date}</span>
                      <span className="text-gray-500">-</span>
                      <span className="text-gray-700">{doc.event_type}</span>
                    </div>
                    <button className="text-blue-600 hover:text-blue-700 flex items-center space-x-1">
                      <ExternalLink className="h-3 w-3" />
                      <span>查看</span>
                    </button>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Document Categories */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h4 className="font-semibold text-gray-900 mb-3">設立文件</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 設立文件</li>
            <li>• 公司章程</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h4 className="font-semibold text-gray-900 mb-3">變更文件</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 股東會會議紀錄</li>
            <li>• 公司章程修訂</li>
            <li>• 股份轉讓書</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h4 className="font-semibold text-gray-900 mb-3">管理文件</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 經理辭任信</li>
            <li>• 新經理接受職務聲明</li>
          </ul>
        </div>
      </div>
    </div>
  )
}