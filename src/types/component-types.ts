import type { BusinessRegistrationData } from '../data/business-registration-data';

// Component prop types
export interface CompanyInfoSectionProps {
  data: BusinessRegistrationData;
  showTimeline?: boolean;
}

export interface DocumentsSectionProps {
  data: BusinessRegistrationData;
}

export interface ShareholdingSectionProps {
  data: BusinessRegistrationData;
  expanded?: boolean;
}

export interface SummaryCardsSectionProps {
  data: BusinessRegistrationData;
}

export interface HeaderProps {
  data: BusinessRegistrationData;
}

// Document types for DocumentsSection
export interface DocumentItem {
  name: string;
  date: string;
  event_type: string;
  event_id: number;
}

export interface DocumentGroup {
  [docType: string]: DocumentItem[];
}

// Chart data types for ShareholdingSection
export interface PieChartData {
  name: string;
  value: number;
  percentage: string;
}

export interface LineChartDataPoint {
  date: string;
  [shareholderName: string]: string | number;
}

// Utility types for component state
export interface HistoryToggleState {
  [key: string]: boolean;
}

// Latest company info type
export interface LatestCompanyInfo {
  address: string;
  businessScope: string;
  capital: number;
}
